import path from 'node:path';
import fs from 'node:fs/promises';
import {readFileParameters, ReadFileParameter} from '../../tool';
import {stringifyError} from '../../utils/error';
import {resultMarkdown, ToolImplementBase, ToolRunResult} from './utils';

export class ReadFileToolImplement extends ToolImplementBase<ReadFileParameter> {
    constructor() {
        super(readFileParameters);
    }

    protected parseArgs(args: Record<string, string>): ReadFileParameter {
        return {
            path: args.path,
        };
    }

    protected async execute(args: ReadFileParameter): Promise<ToolRunResult> {
        try {
            const content = await fs.readFile(path.join(process.cwd(), args.path), 'utf-8');

            if (content === '') {
                return {
                    type: 'success',
                    finished: false,
                    output: `File ${args.path} is an empty file`,
                };
            }

            if (content.length > 30000) {
                return {
                    type: 'success',
                    finished: false,
                    output: `Unable to read file ${args.path}: This file is too large`,
                };
            }

            const language = path.extname(args.path).slice(1);
            return {
                type: 'success',
                finished: false,
                output: resultMarkdown(`Content of file ${args.path}:`, content, language),
            };
        }
        catch (ex) {
            return {
                type: 'executeError',
                output: `Unsable to read file ${args.path}: ${stringifyError(ex)}`,
            };
        }
    }
}
