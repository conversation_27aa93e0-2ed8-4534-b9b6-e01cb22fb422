import OpenAi from 'openai';
import {ModelResponseMetaRecord} from './utils';
import {
    ModelChatOptions,
    ModelClient,
    ModelConfiguration,
    ModelMetaResponse,
    ModelResponse,
    ModelStreamingResponse,
} from './interface';
import * as fs from 'fs';
import * as path from 'path';

const OPEN_ROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

export class OpenAiModelClient implements ModelClient {
    private readonly client: OpenAi;

    private readonly modelName: string;

    constructor(config: ModelConfiguration) {
        const options = {
            apiKey: config.apiKey,
            baseURL: OPEN_ROUTER_BASE_URL,
            defaultHeaders: {
                'HTTP-Referer': 'https://github.com/otakustay/oniichan',
                'X-Title': 'Oniichan',
            },
        };
        this.client = new OpenAi(options);
        this.modelName = config.modelName;
    }

    async chat(options: ModelChatOptions): Promise<[ModelResponse, ModelMetaResponse]> {
        const request = this.getBaseRequest(options);
        const record = new ModelResponseMetaRecord(this.modelName);

        const messages = [...options.messages];
        const outputPath = process.env.CURRENT_OUTPUT_PATH || '';
        if (outputPath) {
            this.saveModelInput(request.messages, outputPath);
        }

        let lastError;
        let retryDelay = 1000; // 初始重试延迟1秒

        while (true) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 600000); // 10分钟超时

                const response = await this.client.chat.completions.create(
                    { ...request },
                    { signal: controller.signal }
                );

                clearTimeout(timeoutId);

                record.setInputTokens(response.usage?.prompt_tokens);
                record.addOutputTokens(response.usage?.completion_tokens);
                return [
                    {type: 'text', content: response.choices[0]?.message?.content ?? ''},
                    record.toResponseMeta(),
                ];
            } catch (error: any) {
                lastError = error;
                const errorType = error.code || error.name || 'UnknownError';
                console.error(`[OpenAI] Request failed (${errorType}), retrying in ${retryDelay}ms:`, error.message);

                if (error.code === 'ETIMEDOUT' || error.name === 'AbortError') {
                    console.error('[OpenAI] Network timeout detected. Possible causes:');
                    console.error('- Unstable network connection');
                    console.error('- Server overload');
                    console.error('- High latency connection');
                }

                await new Promise(resolve => setTimeout(resolve, retryDelay));
                retryDelay = Math.min(retryDelay * 2, 3000); // 指数退避，最大3秒
            }
        }
    }

    async *chatStreaming(options: ModelChatOptions): AsyncIterable<ModelStreamingResponse> {
        const request = {
            ...this.getBaseRequest(options),
            stream: true,
        } as const;

        const messages = [...options.messages];
        const outputPath = process.env.CURRENT_OUTPUT_PATH || '';
        if (outputPath) {
            this.saveModelInput(request.messages, outputPath);
        }

        let lastError;
        let retryDelay = 1000; // 初始重试延迟1秒

        while (true) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 600000); // 10分钟超时

                const stream = await this.client.chat.completions.create(
                    request,
                    { signal: controller.signal }
                );

                clearTimeout(timeoutId);

                const metaRecord = new ModelResponseMetaRecord(this.modelName);

                for await (const chunk of stream) {
                    const delta = chunk.choices.at(0)?.delta;
                    const text = delta?.content ?? '';
                    if (text) {
                        yield {type: 'text', content: text} as const;
                    }
                    metaRecord.setInputTokens(chunk.usage?.prompt_tokens);
                    metaRecord.addOutputTokens(chunk.usage?.completion_tokens);
                }
                yield metaRecord.toResponseMeta();
                return; // 成功完成
            } catch (error: any) {
                lastError = error;
                const errorType = error.code || error.name || 'UnknownError';
                console.error(`[OpenAI] Request failed (${errorType}), retrying in ${retryDelay}ms:`, error.message);

                if (error.code === 'ETIMEDOUT' || error.name === 'AbortError') {
                    console.error('[OpenAI] Network timeout detected. Possible causes:');
                    console.error('- Unstable network connection');
                    console.error('- Server overload');
                    console.error('- High latency connection');
                }

                await new Promise(resolve => setTimeout(resolve, retryDelay));
                retryDelay = Math.min(retryDelay * 2, 3000); // 指数退避，最大3秒
            }
        }
    }

    private getBaseRequest(options: ModelChatOptions) {
        const messages: OpenAi.ChatCompletionMessageParam[] = [...options.messages];
        if (options.systemPrompt) {
            messages.unshift(
                { role: 'user', content: options.systemPrompt },
                { role: 'assistant', content: '好的，我明白了，我是Zulu，我将全力落实之后用户的所有请求' }
            );
        }
        const request: OpenAi.ChatCompletionCreateParams = {
            messages,
            model: this.modelName,
            max_tokens: 8000,
            temperature: 0.7,
            top_p: 0.9
        };
        return request;
    }

    private saveModelInput(messages: any[], outputPath: string, error?: string) {
        try {
            const directory = path.dirname(outputPath);
            const inputPath = path.join(directory, 'case-1-model-input.json');

            const newRecord = {
                messages,
                timestamp: new Date().toISOString(),
                apiUrl: OPEN_ROUTER_BASE_URL,
                modelName: this.modelName,
                error: error || null
            };

            let records = [];
            try {
                if (fs.existsSync(inputPath)) {
                    const content = fs.readFileSync(inputPath, 'utf8');
                    records = JSON.parse(content);
                    if (!Array.isArray(records)) {
                        records = [records];
                    }
                }
            } catch (e) {
                console.error('Error reading existing file:', e);
            }

            records.push(newRecord);
            fs.writeFileSync(inputPath, JSON.stringify(records, null, 2));
            console.log(`Model input saved to ${inputPath}`);
        } catch (error) {
            console.error('Failed to save model input:', error);
        }
    }
}
