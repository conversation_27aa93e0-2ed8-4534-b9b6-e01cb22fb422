import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';
import { ModelClient, ModelChatOptions, ModelResponse, ModelStreamingResponse, ModelMetaResponse } from './interface';
import { ModelResponseMetaRecord } from './utils';

const TIMEOUT_MS = 120000; // 60 seconds timeout

export class LocalDSModelClient implements ModelClient {
    private readonly apiKey: string;
    private readonly modelName: string;
    private readonly apiUrl: string;

    constructor(config: { apiKey: string; modelName: string }) {
        this.apiKey = config.apiKey;
        this.modelName = config.modelName;
        // Model-specific URLs
        this.apiUrl = this.modelName === 'ds3'
            ? 'http://10.14.63.24:8987/v1/chat/completions'
            : 'http://10.14.63.24:8989/v1/chat/completions';
    }

    private saveModelInput(messages: any[], outputPath: string, error?: string) {
        try {
            // 从输出路径获取目录
            const directory = path.dirname(outputPath);
            // 在同一目录下创建输入文件
            const inputPath = path.join(directory, 'case-1-model-input.json');

            // 准备新的记录
            const newRecord = {
                messages,
                timestamp: new Date().toISOString(),
                apiUrl: this.apiUrl,
                modelName: this.modelName,
                error: error || null
            };

            // 读取现有文件（如果存在）
            let records = [];
            try {
                if (fs.existsSync(inputPath)) {
                    const content = fs.readFileSync(inputPath, 'utf8');
                    records = JSON.parse(content);
                    if (!Array.isArray(records)) {
                        records = [records]; // 如果现有内容不是数组，将其转换为数组
                    }
                }
            } catch (e) {
                console.error('Error reading existing file:', e);
            }

            // 添加新记录
            records.push(newRecord);

            // 保存所有记录到文件
            fs.writeFileSync(inputPath, JSON.stringify(records, null, 2));

            console.log(`Model input saved to ${inputPath}`);
        } catch (error) {
            console.error('Failed to save model input:', error);
        }
    }

    async chat(options: ModelChatOptions): Promise<[ModelResponse, ModelMetaResponse]> {
        const messages = [...options.messages];
        if (options.systemPrompt) {
            messages.unshift(
                { role: 'user', content: options.systemPrompt },
                { role: 'assistant', content: '好的，我明白了，我是Zulu，我将全力落实之后用户的所有请求' }
            );
        }

        // 获取输出文件路径
        const outputPath = process.env.CURRENT_OUTPUT_PATH || '';
        if (outputPath) {
            this.saveModelInput(messages, outputPath);
        }

        // Create AbortController for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_MS);

        try {
            const response = await fetch(this.apiUrl, {
                signal: controller.signal,
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`,
                },
                body: JSON.stringify({
                    model: this.modelName.split('@')[0], // Use only the model name part
                    messages,
                    stream: false,
                    max_tokens: 8000,
                    temperature: 0.6
                }),
            });

            if (!response.ok) {
                const error = `Local DS API request failed: ${response.statusText}`;
                if (outputPath) {
                    this.saveModelInput(messages, outputPath, error);
                }
                throw new Error(error);
            }

            const data = await response.json();
            clearTimeout(timeoutId); // Clear timeout after successful response

            const record = new ModelResponseMetaRecord(this.modelName);
            record.setInputTokens(data.usage?.prompt_tokens);
            record.addOutputTokens(data.usage?.completion_tokens);

            return [
                { type: 'text', content: data.choices[0].message.content },
                record.toResponseMeta(),
            ];
        } catch (error) {
            if (outputPath) {
                this.saveModelInput(messages, outputPath, error.message);
            }
            throw error;
        } finally {
            clearTimeout(timeoutId);
        }
        const record = new ModelResponseMetaRecord(this.modelName);

        record.setInputTokens(data.usage?.prompt_tokens);
        record.addOutputTokens(data.usage?.completion_tokens);

        return [
            { type: 'text', content: data.choices[0].message.content },
            record.toResponseMeta(),
        ];
    }

    async *chatStreaming(options: ModelChatOptions): AsyncIterable<ModelStreamingResponse> {
        const messages = [...options.messages];
        if (options.systemPrompt) {
            messages.unshift(
                { role: 'user', content: options.systemPrompt },
                { role: 'assistant', content: '好的，我明白了，我是Zulu，我将全力落实之后用户的所有请求' }
            );
        }

        // 获取输出文件路径
        const outputPath = process.env.CURRENT_OUTPUT_PATH || '';
        if (outputPath) {
            this.saveModelInput(messages, outputPath);
        }

        // Create AbortController for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_MS);

        try {
            console.log('Fetching complete response...');
            // First get the complete response
            const response = await fetch(this.apiUrl, {
                signal: controller.signal,
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`,
                },
                body: JSON.stringify({
                    model: this.modelName.split('@')[0], // Use only the model name part
                    messages,
                    stream: false, // Changed to false to get complete response
                    max_tokens: 8000,
                    temperature: 0.6
                }),
            });

            if (!response.ok) {
                const error = `Local DS API request failed: ${response.statusText}`;
                if (outputPath) {
                    this.saveModelInput(messages, outputPath, error);
                }
                throw new Error(error);
            }

            const data = await response.json();

            if (!data?.choices?.[0]?.message?.content) {
                const error = 'Invalid response format from API';
                if (outputPath) {
                    this.saveModelInput(messages, outputPath, error);
                }
                throw new Error(error);
            }

            // 模拟流式传输响应
            const chunkSize = 4;
            const text = data.choices[0].message.content;
            const metaRecord = new ModelResponseMetaRecord(this.modelName);

            try {
                // Stream the text content
                for (let i = 0; i < text.length; i += chunkSize) {
                    const chunk = text.slice(i, i + chunkSize);
                    yield { type: 'text', content: chunk } as const;
                }

                // Set and yield usage information after streaming the content
                if (data.usage) {
                    metaRecord.setInputTokens(data.usage.prompt_tokens);
                    metaRecord.addOutputTokens(data.usage.completion_tokens);
                }
                yield metaRecord.toResponseMeta();
            } catch (streamError) {
                // If streaming fails, ensure we don't leave the generator hanging
                throw new Error(`Streaming error: ${streamError.message}`);
            }
        } catch (error) {
            if (outputPath) {
                this.saveModelInput(messages, outputPath, error.message);
            }
            throw error;
        } finally {
            clearTimeout(timeoutId);
        }
    }
}
