import {
    DebugMessageLevel,
    DebugContentChunk,
    MessageThread,
    Roundtrip,
    UserRequestMessage,
    ThreadStore,
} from '../message';
import {StreamingToolParser, ToolParsedChunk} from '../tool';
import {stringifyError} from '../utils/error';
import {newUuid} from '../utils/id';
import {WorkflowDetectorInit, WorkflowDetector} from '../workflow';
import {SystemPromptGenerator, SystemPromptOptions} from './prompt';
import {createModelClient, ModelChatOptions} from '../model';
import {GlobalConfig} from './interface';
import fs from 'fs/promises';

async function* chatToText(options: ModelChatOptions) {
    const model = createModelClient();
    for await (const chunk of model.chatStreaming(options)) {
        if (chunk.type === 'text') {
            yield chunk.content;
        } else if (chunk.type === 'meta') {
            yield {
                type: 'meta',
                usage: {
                    inputTokens: chunk.usage.inputTokens,
                    outputTokens: chunk.usage.outputTokens
                }
            };
        }
    }
}

interface TextMessageBody {
    type: 'text';
    content: string;
}

type MessageBody = TextMessageBody;

export interface ComposerRequest {
    threadUuid: string;
    uuid: string;
    body: MessageBody;
}

export interface InboxSendMessageResponse {
    replyUuid: string;
    value: ToolParsedChunk;
}

export interface WorkerOptions {
    globalConfig: GlobalConfig;
    output: string;
    embeddingRepoId: string | null;
    maxTurns?: number; // Maximum number of turns in a dialogue, defaults to 10 if not set
    environment?: string; // New field for environment text
}

export class Worker {
    private readonly store;

    private thread: MessageThread = new MessageThread(newUuid());

    private roundtrip: Roundtrip = new Roundtrip(new UserRequestMessage(newUuid(), ''));

    private readonly systemPromptGenerator: SystemPromptGenerator;

    private systemPrompt = '';

    private totalInputTokens: number = 0;
    private totalOutputTokens: number = 0;
    private currentTurn: number = 0;
    private readonly maxTurns: number | undefined;
    private readonly environment: string;

    constructor(options: WorkerOptions) {
        this.store = new ThreadStore(options.output);
        this.environment = options.environment ?? '';
        const systemProptOptions: SystemPromptOptions = {
            ...options.globalConfig,
            embeddingRepoId: options.embeddingRepoId,
        };
        this.systemPromptGenerator = new SystemPromptGenerator(systemProptOptions);
        this.maxTurns = options.maxTurns ?? 30; // Default to 15 turns if not specified
    }

    async handleRequest(payload: ComposerRequest) {
        this.thread = this.store.ensureThread(payload.threadUuid);
        const combinedContent = this.environment ? `${this.environment}\n\n${payload.body.content}` : payload.body.content;
        this.roundtrip = this.thread.startRoundtrip(new UserRequestMessage(payload.uuid, combinedContent));
        this.currentTurn = 0; // Reset turn counter for new request

        await this.prepareSystemPrompt();
        await this.addDebugMessage('info', 'System Prompt', {type: 'plainText', content: this.systemPrompt});

        await this.requestModel();
    }

    private async requestModel() {
        console.log('Entering requestModel method');
        this.currentTurn++;

        // Check if we've exceeded the maximum number of turns
        if (this.maxTurns && this.currentTurn > this.maxTurns) {
            console.log(`Reached maximum number of turns (${this.maxTurns}). Stopping dialogue.`);
            return;
        }

        let messages = this.thread.toMessages();
        // 保留第一个message(用户原始query)
        const originalQuery = messages[0];

        // 历史信息裁剪逻辑：当总长度超过250K字符时成对裁剪
        const calculateTotalLength = (msgs: typeof messages) =>
            msgs.reduce((sum, msg) => sum + JSON.stringify(msg).length, 0);

        let totalLength = calculateTotalLength(messages);
        while (totalLength > 320 * 1024 && messages.length > 1) {
            // 成对移除(保留原始query)
            messages = [originalQuery, ...messages.slice(3)];
            totalLength = calculateTotalLength(messages);
            console.log(`History trimmed to ${messages.length} messages, total length: ${totalLength}`);
        }

        const reply = this.roundtrip.startTextResponse(newUuid());
        console.log(`<---------- start message ${reply.uuid} ---------->`);

        const options: ModelChatOptions = {
            messages: messages.map(v => v.toChatInputPayload()).filter(v => !!v),
            systemPrompt: this.systemPrompt,
        };
        const parser = new StreamingToolParser();

        // 用于记录完整的模型输出
        const completeModelOutput: string[] = [];
        let modelInputPath = '';

        // 辅助函数：将字符串数组转换为 AsyncIterable<string>
        async function* arrayToAsyncIterable(arr: string[]): AsyncIterable<string> {
            yield arr.join('');
        }

        try {
            // 准备日志文件路径
            const outputPath = (this.store as any).outputPath;
            if (outputPath) {
                modelInputPath = outputPath.replace('output.json', 'error-model-input.json');
                // 记录输入信息
                const inputInfo = {
                    systemPrompt: this.systemPrompt,
                    messages: messages.map(v => v.toChatInputPayload()).filter(v => !!v)
                };
                await fs.writeFile(modelInputPath, JSON.stringify(inputInfo, null, 2));
                console.log('Saved model input to:', modelInputPath);
            }

            console.log('Starting to process model response');
            // 创建一个字符串数组来累积文本内容
            const textChunks: string[] = [];

            try {
                for await (const chunk of chatToText(options)) {
                    if (chunk && typeof chunk === 'object' && 'type' in chunk && chunk.type === 'meta') {
                        // 更安全的meta数据处理
                        const usage = chunk.usage || {};
                        const inputTokens = Number(usage.inputTokens) || 0;
                        const outputTokens = Number(usage.outputTokens) || 0;
                        this.totalInputTokens += inputTokens;
                        this.totalOutputTokens += outputTokens;
                        console.log('Token Usage:', {
                            input: inputTokens,
                            output: outputTokens
                        });
                    } else if (typeof chunk === 'string') {
                        // 确保chunk是字符串
                        textChunks.push(chunk);
                        completeModelOutput.push(chunk);
                    } else {
                        console.warn('Unexpected chunk type:', typeof chunk, chunk);
                    }
                }
            } catch (err) {
                console.error('Error processing model response:', err);
                throw err;
            }

            console.info('==========Complete model output=============');
            console.info(completeModelOutput.join(''))
            console.info('==========end Complete model output=============');


            // 将收集到的文本内容作为一个完整的流传给解析器
            try {
                for await (const parsedChunk of parser.parse(arrayToAsyncIterable(textChunks))) {
                    const text = parsedChunk.type === 'text' ? parsedChunk.content : parsedChunk.source;
                    process.stdout.write(text);
                    reply.addChunk(parsedChunk);
                }
            } catch (error) {
                console.error('Parser Error:', error);
                console.error('Error details:', {
                    textChunksLength: textChunks.length,
                    textChunksSample: textChunks.length > 0 ? textChunks[0].slice(0, 100) : 'empty'
                });
                process.exit(1);
            }
            console.log('Finished processing model response with parsed tools:');
            const replyData = reply.toMessageData();
            console.log('Reply content:', reply.getTextContent());
            console.log('Reply object details:', {
                uuid: reply.uuid,
                chunks: replyData.chunks.map(c => ({
                    type: c.type,
                    content: c.type === 'text' ? c.content.slice(0, 100) + (c.content.length > 100 ? '...' : '') :
                            c.type === 'toolCall' ? c.source.slice(0, 100) + (c.source.length > 100 ? '...' : '') :
                            c.type === 'thinking' ? c.content.slice(0, 100) + (c.content.length > 100 ? '...' : '') : '',
                    length: c.type === 'text' ? c.content.length :
                            c.type === 'toolCall' ? c.source.length :
                            c.type === 'thinking' ? c.content.length : 0
                })),
                error: reply.error,
                isComplete: replyData.chunks.some(c => c.type === 'toolCall'),
                createdAt: reply.createdAt
            });
        }
        catch (ex) {
            console.error('Error in requestModel:', ex);
            console.error('==========Complete model output=============');
            console.error(completeModelOutput.join(''))
            console.error('==========end Complete model output=============');

            // 在错误发生时保存完整的模型输出
            if (modelInputPath) {
                const errorLogPath = modelInputPath.replace('error-model-input.json', 'error.log');
                const errorLog = {
                    error: stringifyError(ex),
                    completeModelOutput: completeModelOutput.join(''),
                    timestamp: new Date().toISOString()
                };
                try {
                    await fs.writeFile(errorLogPath, JSON.stringify(errorLog, null, 2));
                    console.error('Error details saved to:', errorLogPath);
                } catch (writeError) {
                    console.error('Failed to write error log:', writeError);
                }
            }

            reply.setError(stringifyError(ex));
            throw ex;
        }
        finally {
            console.log('Exiting requestModel method');
        }

        // 在方法结束时更新 token 统计
        console.log('store token usage');
        (this.store as ThreadStore).setTokenUsage(this.totalInputTokens, this.totalOutputTokens);
        await this.store.persist();
        console.log('finished store token usage');
        const detectorInit: WorkflowDetectorInit = {
            threadUuid: this.thread.uuid,
            systemPrompt: this.systemPrompt,
            roundtrip: this.roundtrip,
            store: this.store,
        };
        const detector = new WorkflowDetector(detectorInit);
        console.log('Workflow detector created');
        const workflowRunner = detector.detectWorkflow();
        console.log('Workflow detected:', !!workflowRunner);

        if (workflowRunner) {
            console.log('Starting workflow run');
            await workflowRunner.run();
            console.log('Workflow run completed');

            const shouldContinue = workflowRunner.getWorkflow().shouldContinueRoundtrip();
            console.log('Should continue roundtrip:', shouldContinue);

            if (shouldContinue) {
                console.log('Continuing to next round');
                await this.requestModel();
            } else {
                console.log(`end of conversation, store the last message`);
                const messages = this.thread.toMessages();
                const reply = this.roundtrip.startTextResponse(newUuid());
                const options: ModelChatOptions = {
                    messages: messages.map(v => v.toChatInputPayload()).filter(v => !!v),
                    systemPrompt: this.systemPrompt,
                };
                for await (const chunk of chatToText(options)) {
                    break;
                }
                console.log('Workflow completed, not continuing');
            }
        }
    }

    private async prepareSystemPrompt() {
        this.systemPromptGenerator.setUserRequest(this.roundtrip.getRequestText());
        this.systemPrompt = await this.systemPromptGenerator.renderSystemPrompt();
        console.log('Worker prepareSystemPrompt - systemPrompt:', this.systemPrompt);
    }

    private async addDebugMessage(level: DebugMessageLevel, title: string, content: DebugContentChunk) {
        this.roundtrip.addDebugMessage(newUuid(), level, title, content);
        await this.store.persist();
    }
}
